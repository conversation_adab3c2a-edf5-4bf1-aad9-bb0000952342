<?xml version="1.0" encoding="UTF-8"?>
<Scheme
   LastUpgradeVersion = "1250"
   wasCreatedForAppExtension = "YES"
   version = "2.0">
   <BuildAction
      parallelizeBuildables = "YES"
      buildImplicitDependencies = "YES">
      <BuildActionEntries>
         <BuildActionEntry
            buildForTesting = "YES"
            buildForRunning = "YES"
            buildForProfiling = "YES"
            buildForArchiving = "YES"
            buildForAnalyzing = "YES">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "4574400320B890A000E6336E"
               BuildableName = "OneSignalNotificationServiceExtension.appex"
               BlueprintName = "OneSignalNotificationServiceExtension"
               ReferencedContainer = "container:WebViewGold.xcodeproj">
            </BuildableReference>
         </BuildActionEntry>
         <BuildActionEntry
            buildForTesting = "YES"
            buildForRunning = "YES"
            buildForProfiling = "YES"
            buildForArchiving = "YES"
            buildForAnalyzing = "YES">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "8171D2071F6B096F00AA7B99"
               BuildableName = "&#x645;&#x646;&#x635;&#x629; &#x627;&#x644;&#x62f;&#x644;&#x641;&#x64a;&#x646;.app"
               BlueprintName = "WebViewGold"
               ReferencedContainer = "container:WebViewGold.xcodeproj">
            </BuildableReference>
         </BuildActionEntry>
      </BuildActionEntries>
   </BuildAction>
   <TestAction
      buildConfiguration = "Debug"
      selectedDebuggerIdentifier = "Xcode.DebuggerFoundation.Debugger.LLDB"
      selectedLauncherIdentifier = "Xcode.DebuggerFoundation.Launcher.LLDB"
      shouldUseLaunchSchemeArgsEnv = "YES">
      <MacroExpansion>
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "4574400320B890A000E6336E"
            BuildableName = "OneSignalNotificationServiceExtension.appex"
            BlueprintName = "OneSignalNotificationServiceExtension"
            ReferencedContainer = "container:WebViewGold.xcodeproj">
         </BuildableReference>
      </MacroExpansion>
      <Testables>
      </Testables>
   </TestAction>
   <LaunchAction
      buildConfiguration = "Debug"
      selectedDebuggerIdentifier = ""
      selectedLauncherIdentifier = "Xcode.IDEFoundation.Launcher.PosixSpawn"
      launchStyle = "0"
      askForAppToLaunch = "Yes"
      useCustomWorkingDirectory = "NO"
      ignoresPersistentStateOnLaunch = "NO"
      debugDocumentVersioning = "YES"
      debugServiceExtension = "internal"
      allowLocationSimulation = "YES"
      launchAutomaticallySubstyle = "2">
      <BuildableProductRunnable
         runnableDebuggingMode = "0">
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "8171D2071F6B096F00AA7B99"
            BuildableName = "&#x645;&#x646;&#x635;&#x629; &#x627;&#x644;&#x62f;&#x644;&#x641;&#x64a;&#x646;.app"
            BlueprintName = "WebViewGold"
            ReferencedContainer = "container:WebViewGold.xcodeproj">
         </BuildableReference>
      </BuildableProductRunnable>
   </LaunchAction>
   <ProfileAction
      buildConfiguration = "Release"
      shouldUseLaunchSchemeArgsEnv = "YES"
      savedToolIdentifier = ""
      useCustomWorkingDirectory = "NO"
      debugDocumentVersioning = "YES"
      launchAutomaticallySubstyle = "2">
      <BuildableProductRunnable
         runnableDebuggingMode = "0">
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "8171D2071F6B096F00AA7B99"
            BuildableName = "&#x645;&#x646;&#x635;&#x629; &#x627;&#x644;&#x62f;&#x644;&#x641;&#x64a;&#x646;.app"
            BlueprintName = "WebViewGold"
            ReferencedContainer = "container:WebViewGold.xcodeproj">
         </BuildableReference>
      </BuildableProductRunnable>
   </ProfileAction>
   <AnalyzeAction
      buildConfiguration = "Debug">
   </AnalyzeAction>
   <ArchiveAction
      buildConfiguration = "Release"
      revealArchiveInOrganizer = "YES">
   </ArchiveAction>
</Scheme>
