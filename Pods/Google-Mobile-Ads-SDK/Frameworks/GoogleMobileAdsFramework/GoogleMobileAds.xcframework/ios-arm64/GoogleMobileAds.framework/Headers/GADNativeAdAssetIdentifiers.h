//
//  GADNativeAdAssetIdentifiers.h
//  Google Mobile Ads SDK
//
//  Copyright 2017 Google LLC. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <GoogleMobileAds/GoogleMobileAdsDefines.h>

typedef NSString *GADNativeAssetIdentifier NS_TYPED_ENUM;

FOUNDATION_EXPORT GADNativeAssetIdentifier _Nonnull const GADNativeHeadlineAsset;
FOUNDATION_EXPORT GADNativeAssetIdentifier _Nonnull const GADNativeCallToActionAsset;
FOUNDATION_EXPORT GADNativeAssetIdentifier _Nonnull const GADNativeIconAsset;
FOUNDATION_EXPORT GADNativeAssetIdentifier _Nonnull const GADNativeBodyAsset;
FOUNDATION_EXPORT GADNativeAssetIdentifier _Nonnull const GADNativeStoreAsset;
FOUNDATION_EXPORT GADNativeAssetIdentifier _Nonnull const GADNativePriceAsset;
FOUNDATION_EXPORT GADNativeAssetIdentifier _Nonnull const GADNativeImageAsset;
FOUNDATION_EXPORT GADNativeAssetIdentifier _Nonnull const GADNativeStarRatingAsset;
FOUNDATION_EXPORT GADNativeAssetIdentifier _Nonnull const GADNativeAdvertiserAsset;
FOUNDATION_EXPORT GADNativeAssetIdentifier _Nonnull const GADNativeMediaViewAsset;
FOUNDATION_EXPORT GADNativeAssetIdentifier _Nonnull const GADNativeAdChoicesViewAsset;
