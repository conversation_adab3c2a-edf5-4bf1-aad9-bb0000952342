<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>SchemeUserState</key>
	<dict>
		<key>FBAudienceNetwork-FBAudienceNetwork.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>28</integer>
		</dict>
		<key>FBAudienceNetwork.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>12</integer>
		</dict>
		<key>Firebase.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>19</integer>
		</dict>
		<key>FirebaseAnalytics.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>26</integer>
		</dict>
		<key>FirebaseCore-FirebaseCore_Privacy.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>8</integer>
		</dict>
		<key>FirebaseCore.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>7</integer>
		</dict>
		<key>FirebaseCoreInternal-FirebaseCoreInternal_Privacy.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>27</integer>
		</dict>
		<key>FirebaseCoreInternal.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>32</integer>
		</dict>
		<key>FirebaseInstallations-FirebaseInstallations_Privacy.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>33</integer>
		</dict>
		<key>FirebaseInstallations.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>13</integer>
		</dict>
		<key>FirebaseMessaging-FirebaseMessaging_Privacy.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>4</integer>
		</dict>
		<key>FirebaseMessaging.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>9</integer>
		</dict>
		<key>Google-Mobile-Ads-SDK-GoogleMobileAdsResources.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>21</integer>
		</dict>
		<key>Google-Mobile-Ads-SDK.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>25</integer>
		</dict>
		<key>GoogleAppMeasurement.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>6</integer>
		</dict>
		<key>GoogleDataTransport-GoogleDataTransport_Privacy.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>15</integer>
		</dict>
		<key>GoogleDataTransport.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>14</integer>
		</dict>
		<key>GoogleUserMessagingPlatform-UserMessagingPlatformResources.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>2</integer>
		</dict>
		<key>GoogleUserMessagingPlatform.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>29</integer>
		</dict>
		<key>GoogleUtilities-GoogleUtilities_Privacy.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>17</integer>
		</dict>
		<key>GoogleUtilities.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>24</integer>
		</dict>
		<key>OneSignalXCFramework.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>10</integer>
		</dict>
		<key>Pods-OneSignalNotificationServiceExtension.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>20</integer>
		</dict>
		<key>Pods-WebViewGold.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>11</integer>
		</dict>
		<key>PromisesObjC-FBLPromises_Privacy.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>5</integer>
		</dict>
		<key>PromisesObjC.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>3</integer>
		</dict>
		<key>PushwooshXCFramework.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>31</integer>
		</dict>
		<key>SwiftQRScanner.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>30</integer>
		</dict>
		<key>SwiftyGif-SwiftyGif.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>34</integer>
		</dict>
		<key>SwiftyGif.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>23</integer>
		</dict>
		<key>SwiftyStoreKit.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>18</integer>
		</dict>
		<key>nanopb-nanopb_Privacy.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>22</integer>
		</dict>
		<key>nanopb.xcscheme_^#shared#^_</key>
		<dict>
			<key>orderHint</key>
			<integer>16</integer>
		</dict>
	</dict>
</dict>
</plist>
