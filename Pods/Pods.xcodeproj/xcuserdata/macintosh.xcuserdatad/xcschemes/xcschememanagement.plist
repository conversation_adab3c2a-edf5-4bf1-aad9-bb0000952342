<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>SchemeUserState</key>
	<dict>
		<key>FBAudienceNetwork-FBAudienceNetwork.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>FBAudienceNetwork.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>Firebase.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>FirebaseAnalytics.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>FirebaseCore-FirebaseCore_Privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>FirebaseCore.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>FirebaseCoreInternal-FirebaseCoreInternal_Privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>FirebaseCoreInternal.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>FirebaseInstallations-FirebaseInstallations_Privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>FirebaseInstallations.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>FirebaseMessaging-FirebaseMessaging_Privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>FirebaseMessaging.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>Google-Mobile-Ads-SDK-GoogleMobileAdsResources.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>Google-Mobile-Ads-SDK.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>GoogleAppMeasurement.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>GoogleDataTransport-GoogleDataTransport_Privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>GoogleDataTransport.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>GoogleUserMessagingPlatform-UserMessagingPlatformResources.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>GoogleUserMessagingPlatform.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>GoogleUtilities-GoogleUtilities_Privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>GoogleUtilities.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>OneSignalXCFramework.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>Pods-OneSignalNotificationServiceExtension.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>Pods-WebViewGold.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>PromisesObjC-FBLPromises_Privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>PromisesObjC.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>PushwooshXCFramework.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>SwiftQRScanner.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>SwiftyGif-SwiftyGif.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>SwiftyGif.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>SwiftyStoreKit.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>nanopb-nanopb_Privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>nanopb.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
	</dict>
	<key>SuppressBuildableAutocreation</key>
	<dict/>
</dict>
</plist>
