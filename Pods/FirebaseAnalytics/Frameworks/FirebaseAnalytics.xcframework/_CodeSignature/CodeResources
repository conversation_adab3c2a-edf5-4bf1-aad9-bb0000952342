<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>ios-arm64/FirebaseAnalytics.framework/FirebaseAnalytics</key>
		<data>
		AYtP7YzjjZ6xReUnCNfX3B8fngg=
		</data>
		<key>ios-arm64/FirebaseAnalytics.framework/Headers/FIRAnalytics+AppDelegate.h</key>
		<data>
		JxSy4BVIpZB3s+tbI3EgcIVsvN0=
		</data>
		<key>ios-arm64/FirebaseAnalytics.framework/Headers/FIRAnalytics+Consent.h</key>
		<data>
		IN4riVkldIj3MlPpvlUuU1i+I8w=
		</data>
		<key>ios-arm64/FirebaseAnalytics.framework/Headers/FIRAnalytics+OnDevice.h</key>
		<data>
		58LqQ/Y9jqPGz3JttzK0z7jPLLc=
		</data>
		<key>ios-arm64/FirebaseAnalytics.framework/Headers/FIRAnalytics.h</key>
		<data>
		B+YLaEHhiOu6tTPZaB5wWy4n95U=
		</data>
		<key>ios-arm64/FirebaseAnalytics.framework/Headers/FIREventNames.h</key>
		<data>
		HrzLufMtB3E5zAfPXZTkYQdGyHI=
		</data>
		<key>ios-arm64/FirebaseAnalytics.framework/Headers/FIRParameterNames.h</key>
		<data>
		SztBbCRZ5QE523pdMx4HZt0AYKE=
		</data>
		<key>ios-arm64/FirebaseAnalytics.framework/Headers/FIRUserPropertyNames.h</key>
		<data>
		33ogzLW88kbc3XVXJp9Opq9Znmc=
		</data>
		<key>ios-arm64/FirebaseAnalytics.framework/Headers/FirebaseAnalytics-Swift.h</key>
		<data>
		0nWXABx/HRqcUMkqMxRdJuHxqQU=
		</data>
		<key>ios-arm64/FirebaseAnalytics.framework/Headers/FirebaseAnalytics-umbrella.h</key>
		<data>
		wE8Bjq1o5wGq1TFJb0gCJUmRelQ=
		</data>
		<key>ios-arm64/FirebaseAnalytics.framework/Headers/FirebaseAnalytics.h</key>
		<data>
		6tM+QmAiCFyFHMaFXgWsH/uYosM=
		</data>
		<key>ios-arm64/FirebaseAnalytics.framework/Info.plist</key>
		<data>
		24GbDwHEExCsmAqEMaAnPX7hRng=
		</data>
		<key>ios-arm64/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/Project/arm64-apple-ios.swiftsourceinfo</key>
		<data>
		PAEFPn/Z87grIPt1TCrs17n93x0=
		</data>
		<key>ios-arm64/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios.abi.json</key>
		<data>
		CAenCYd3LH1mXzWkxe5ejHMDkGU=
		</data>
		<key>ios-arm64/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios.private.swiftinterface</key>
		<data>
		PTzHQuWqC1lwO+qReE+xp0WO1no=
		</data>
		<key>ios-arm64/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios.swiftdoc</key>
		<data>
		OaDLqia2HgSvNF2HhREihK6wwYI=
		</data>
		<key>ios-arm64/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios.swiftinterface</key>
		<data>
		PTzHQuWqC1lwO+qReE+xp0WO1no=
		</data>
		<key>ios-arm64/FirebaseAnalytics.framework/Modules/module.modulemap</key>
		<data>
		uLysK0T5K1GSoqJmcXx9AnZ9lqY=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/FirebaseAnalytics</key>
		<data>
		42q76dI3y9rQrjqqUTNWwhZ7Jks=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Headers/FIRAnalytics+AppDelegate.h</key>
		<data>
		JxSy4BVIpZB3s+tbI3EgcIVsvN0=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Headers/FIRAnalytics+Consent.h</key>
		<data>
		IN4riVkldIj3MlPpvlUuU1i+I8w=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Headers/FIRAnalytics+OnDevice.h</key>
		<data>
		58LqQ/Y9jqPGz3JttzK0z7jPLLc=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Headers/FIRAnalytics.h</key>
		<data>
		B+YLaEHhiOu6tTPZaB5wWy4n95U=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Headers/FIREventNames.h</key>
		<data>
		HrzLufMtB3E5zAfPXZTkYQdGyHI=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Headers/FIRParameterNames.h</key>
		<data>
		SztBbCRZ5QE523pdMx4HZt0AYKE=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Headers/FIRUserPropertyNames.h</key>
		<data>
		33ogzLW88kbc3XVXJp9Opq9Znmc=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Headers/FirebaseAnalytics-Swift.h</key>
		<data>
		cu0mXcU7RrT9nKAMSamYxQcZ8MA=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Headers/FirebaseAnalytics-umbrella.h</key>
		<data>
		wE8Bjq1o5wGq1TFJb0gCJUmRelQ=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Headers/FirebaseAnalytics.h</key>
		<data>
		6tM+QmAiCFyFHMaFXgWsH/uYosM=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/Project/arm64-apple-ios-macabi.swiftsourceinfo</key>
		<data>
		HNZ2Jnf3ch0f2ZbmLUXV1LrWM/0=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/Project/x86_64-apple-ios-macabi.swiftsourceinfo</key>
		<data>
		pJXoMAYTHcNc6A+wok94J0Z3PzQ=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios-macabi.abi.json</key>
		<data>
		CAenCYd3LH1mXzWkxe5ejHMDkGU=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios-macabi.private.swiftinterface</key>
		<data>
		4gR6vGh9NGCOc8NqbJitBi4PYMc=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios-macabi.swiftdoc</key>
		<data>
		qhoelsD3haNmZEE9NM8xvtK9mEY=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios-macabi.swiftinterface</key>
		<data>
		4gR6vGh9NGCOc8NqbJitBi4PYMc=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-ios-macabi.abi.json</key>
		<data>
		CAenCYd3LH1mXzWkxe5ejHMDkGU=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-ios-macabi.private.swiftinterface</key>
		<data>
		R1LVVji18GwfW9nhhjO5aFGB1+k=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-ios-macabi.swiftdoc</key>
		<data>
		/8RdTbctZKCA1/OIwmZLl2oWnCk=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-ios-macabi.swiftinterface</key>
		<data>
		R1LVVji18GwfW9nhhjO5aFGB1+k=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Modules/module.modulemap</key>
		<data>
		uLysK0T5K1GSoqJmcXx9AnZ9lqY=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Resources/Info.plist</key>
		<data>
		OzhUmy4DzK3Rxlq/bU6nkujbFmc=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/FirebaseAnalytics</key>
		<data>
		QM6Y6wt9cmkeZQYdeaHFCtKfH4s=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRAnalytics+AppDelegate.h</key>
		<data>
		JxSy4BVIpZB3s+tbI3EgcIVsvN0=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRAnalytics+Consent.h</key>
		<data>
		IN4riVkldIj3MlPpvlUuU1i+I8w=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRAnalytics+OnDevice.h</key>
		<data>
		58LqQ/Y9jqPGz3JttzK0z7jPLLc=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRAnalytics.h</key>
		<data>
		B+YLaEHhiOu6tTPZaB5wWy4n95U=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIREventNames.h</key>
		<data>
		HrzLufMtB3E5zAfPXZTkYQdGyHI=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRParameterNames.h</key>
		<data>
		SztBbCRZ5QE523pdMx4HZt0AYKE=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRUserPropertyNames.h</key>
		<data>
		33ogzLW88kbc3XVXJp9Opq9Znmc=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FirebaseAnalytics-Swift.h</key>
		<data>
		cu0mXcU7RrT9nKAMSamYxQcZ8MA=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FirebaseAnalytics-umbrella.h</key>
		<data>
		wE8Bjq1o5wGq1TFJb0gCJUmRelQ=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FirebaseAnalytics.h</key>
		<data>
		6tM+QmAiCFyFHMaFXgWsH/uYosM=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Info.plist</key>
		<data>
		a/ELmaanMcGm4o8ePSQhapLfN8g=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		DBvAE3VnjSOG5P478X3Putqnngk=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		6XJlLJUNSEr2mzRdEdwredClK0o=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<data>
		CAenCYd3LH1mXzWkxe5ejHMDkGU=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios-simulator.private.swiftinterface</key>
		<data>
		QinHhX49jrG/LCXfKglixhg2PPE=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<data>
		2uhhvWC34TCjqIKVVlqG/EycMGY=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios-simulator.swiftinterface</key>
		<data>
		QinHhX49jrG/LCXfKglixhg2PPE=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<data>
		CAenCYd3LH1mXzWkxe5ejHMDkGU=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<data>
		0qZBNnrrWfJHxGSgyJ0H3df9gbQ=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<data>
		lW9AObcye0EJ2DSm5O07/pplg2Y=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<data>
		0qZBNnrrWfJHxGSgyJ0H3df9gbQ=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/module.modulemap</key>
		<data>
		uLysK0T5K1GSoqJmcXx9AnZ9lqY=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/FirebaseAnalytics</key>
		<data>
		vOVrRvYINMVRHZwAzEWPe6FgfsA=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Headers/FIRAnalytics+AppDelegate.h</key>
		<data>
		JxSy4BVIpZB3s+tbI3EgcIVsvN0=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Headers/FIRAnalytics+Consent.h</key>
		<data>
		IN4riVkldIj3MlPpvlUuU1i+I8w=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Headers/FIRAnalytics+OnDevice.h</key>
		<data>
		58LqQ/Y9jqPGz3JttzK0z7jPLLc=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Headers/FIRAnalytics.h</key>
		<data>
		B+YLaEHhiOu6tTPZaB5wWy4n95U=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Headers/FIREventNames.h</key>
		<data>
		HrzLufMtB3E5zAfPXZTkYQdGyHI=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Headers/FIRParameterNames.h</key>
		<data>
		SztBbCRZ5QE523pdMx4HZt0AYKE=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Headers/FIRUserPropertyNames.h</key>
		<data>
		33ogzLW88kbc3XVXJp9Opq9Znmc=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Headers/FirebaseAnalytics-Swift.h</key>
		<data>
		cu0mXcU7RrT9nKAMSamYxQcZ8MA=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Headers/FirebaseAnalytics-umbrella.h</key>
		<data>
		sXk7jWhcfsvb8eJb7/NDl2Dy2RQ=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Headers/FirebaseAnalytics.h</key>
		<data>
		6tM+QmAiCFyFHMaFXgWsH/uYosM=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo</key>
		<data>
		oHwEH8nDNQLi2WiJYNHluEM3dr8=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/Project/x86_64-apple-macos.swiftsourceinfo</key>
		<data>
		5D2gHeLqXEwVMH2q9LsSCJvZ3Hw=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-macos.abi.json</key>
		<data>
		CAenCYd3LH1mXzWkxe5ejHMDkGU=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-macos.private.swiftinterface</key>
		<data>
		8GJRLSkXpWDzmNzbFI7E5MPaxHs=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-macos.swiftdoc</key>
		<data>
		/do/AsQ2BlA5B8FGwqiGoGnBanw=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-macos.swiftinterface</key>
		<data>
		8GJRLSkXpWDzmNzbFI7E5MPaxHs=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-macos.abi.json</key>
		<data>
		CAenCYd3LH1mXzWkxe5ejHMDkGU=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-macos.private.swiftinterface</key>
		<data>
		IEN4ZkUAv93Vhe3Ywy2Ti9s/jSw=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-macos.swiftdoc</key>
		<data>
		bobvJ33JqODvJfRuJhtThgX6/9U=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-macos.swiftinterface</key>
		<data>
		IEN4ZkUAv93Vhe3Ywy2Ti9s/jSw=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Modules/module.modulemap</key>
		<data>
		jWZ+azdgXQdH9z56z25NptmWlq4=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Resources/Info.plist</key>
		<data>
		Ohh69ZLP3Pwmu3LTZbXKbBKfbGU=
		</data>
		<key>tvos-arm64/FirebaseAnalytics.framework/FirebaseAnalytics</key>
		<data>
		W8q251W3WIRDyG0bRchN+yib+J0=
		</data>
		<key>tvos-arm64/FirebaseAnalytics.framework/Headers/FIRAnalytics+AppDelegate.h</key>
		<data>
		JxSy4BVIpZB3s+tbI3EgcIVsvN0=
		</data>
		<key>tvos-arm64/FirebaseAnalytics.framework/Headers/FIRAnalytics+Consent.h</key>
		<data>
		IN4riVkldIj3MlPpvlUuU1i+I8w=
		</data>
		<key>tvos-arm64/FirebaseAnalytics.framework/Headers/FIRAnalytics+OnDevice.h</key>
		<data>
		58LqQ/Y9jqPGz3JttzK0z7jPLLc=
		</data>
		<key>tvos-arm64/FirebaseAnalytics.framework/Headers/FIRAnalytics.h</key>
		<data>
		B+YLaEHhiOu6tTPZaB5wWy4n95U=
		</data>
		<key>tvos-arm64/FirebaseAnalytics.framework/Headers/FIREventNames.h</key>
		<data>
		HrzLufMtB3E5zAfPXZTkYQdGyHI=
		</data>
		<key>tvos-arm64/FirebaseAnalytics.framework/Headers/FIRParameterNames.h</key>
		<data>
		SztBbCRZ5QE523pdMx4HZt0AYKE=
		</data>
		<key>tvos-arm64/FirebaseAnalytics.framework/Headers/FIRUserPropertyNames.h</key>
		<data>
		33ogzLW88kbc3XVXJp9Opq9Znmc=
		</data>
		<key>tvos-arm64/FirebaseAnalytics.framework/Headers/FirebaseAnalytics-Swift.h</key>
		<data>
		0nWXABx/HRqcUMkqMxRdJuHxqQU=
		</data>
		<key>tvos-arm64/FirebaseAnalytics.framework/Headers/FirebaseAnalytics-umbrella.h</key>
		<data>
		wE8Bjq1o5wGq1TFJb0gCJUmRelQ=
		</data>
		<key>tvos-arm64/FirebaseAnalytics.framework/Headers/FirebaseAnalytics.h</key>
		<data>
		6tM+QmAiCFyFHMaFXgWsH/uYosM=
		</data>
		<key>tvos-arm64/FirebaseAnalytics.framework/Info.plist</key>
		<data>
		6aXVQGe8JpqinvGQ6cWiSR2Srr0=
		</data>
		<key>tvos-arm64/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/Project/arm64-apple-tvos.swiftsourceinfo</key>
		<data>
		zxaMs9ukSdkkXZqwV3vOHeWr7+g=
		</data>
		<key>tvos-arm64/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-tvos.abi.json</key>
		<data>
		CAenCYd3LH1mXzWkxe5ejHMDkGU=
		</data>
		<key>tvos-arm64/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-tvos.private.swiftinterface</key>
		<data>
		w4z4Z/v+RhKvKZ9rpQwngKxdIv8=
		</data>
		<key>tvos-arm64/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-tvos.swiftdoc</key>
		<data>
		hXtRlFapwJDHg4NuSRJKOZnPtf4=
		</data>
		<key>tvos-arm64/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-tvos.swiftinterface</key>
		<data>
		w4z4Z/v+RhKvKZ9rpQwngKxdIv8=
		</data>
		<key>tvos-arm64/FirebaseAnalytics.framework/Modules/module.modulemap</key>
		<data>
		uLysK0T5K1GSoqJmcXx9AnZ9lqY=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/FirebaseAnalytics</key>
		<data>
		9mJR8rSmTn7mt3W8hEw0jnxQfN0=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRAnalytics+AppDelegate.h</key>
		<data>
		JxSy4BVIpZB3s+tbI3EgcIVsvN0=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRAnalytics+Consent.h</key>
		<data>
		IN4riVkldIj3MlPpvlUuU1i+I8w=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRAnalytics+OnDevice.h</key>
		<data>
		58LqQ/Y9jqPGz3JttzK0z7jPLLc=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRAnalytics.h</key>
		<data>
		B+YLaEHhiOu6tTPZaB5wWy4n95U=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIREventNames.h</key>
		<data>
		HrzLufMtB3E5zAfPXZTkYQdGyHI=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRParameterNames.h</key>
		<data>
		SztBbCRZ5QE523pdMx4HZt0AYKE=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRUserPropertyNames.h</key>
		<data>
		33ogzLW88kbc3XVXJp9Opq9Znmc=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FirebaseAnalytics-Swift.h</key>
		<data>
		cu0mXcU7RrT9nKAMSamYxQcZ8MA=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FirebaseAnalytics-umbrella.h</key>
		<data>
		wE8Bjq1o5wGq1TFJb0gCJUmRelQ=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FirebaseAnalytics.h</key>
		<data>
		6tM+QmAiCFyFHMaFXgWsH/uYosM=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Info.plist</key>
		<data>
		kXJVN191XwsiAoBr3QwAXliqK64=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/Project/arm64-apple-tvos-simulator.swiftsourceinfo</key>
		<data>
		AaIax21fSETeDoYqQ8cVecFc1DA=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/Project/x86_64-apple-tvos-simulator.swiftsourceinfo</key>
		<data>
		r/Iaqox1b0S0D1nrKMmpUz/RYs0=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-tvos-simulator.abi.json</key>
		<data>
		CAenCYd3LH1mXzWkxe5ejHMDkGU=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-tvos-simulator.private.swiftinterface</key>
		<data>
		Hf8XfWJzIeEvfdJv/6AMdURHy9k=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-tvos-simulator.swiftdoc</key>
		<data>
		6OlHbfwffHQPeO54yIdFziP5xxg=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-tvos-simulator.swiftinterface</key>
		<data>
		Hf8XfWJzIeEvfdJv/6AMdURHy9k=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-tvos-simulator.abi.json</key>
		<data>
		CAenCYd3LH1mXzWkxe5ejHMDkGU=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-tvos-simulator.private.swiftinterface</key>
		<data>
		/J9MSWx/8Fj0oVu98k1n8XZmnUE=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-tvos-simulator.swiftdoc</key>
		<data>
		foK4LqUJlIKa+nt0y/51HYQeK8I=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-tvos-simulator.swiftinterface</key>
		<data>
		/J9MSWx/8Fj0oVu98k1n8XZmnUE=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/module.modulemap</key>
		<data>
		uLysK0T5K1GSoqJmcXx9AnZ9lqY=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>ios-arm64/FirebaseAnalytics.framework/FirebaseAnalytics</key>
		<dict>
			<key>hash</key>
			<data>
			AYtP7YzjjZ6xReUnCNfX3B8fngg=
			</data>
			<key>hash2</key>
			<data>
			HVWK35xmkw3B3y6ncbThNS61VSi6wSeRFnAP4C2/g7s=
			</data>
		</dict>
		<key>ios-arm64/FirebaseAnalytics.framework/Headers/FIRAnalytics+AppDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			JxSy4BVIpZB3s+tbI3EgcIVsvN0=
			</data>
			<key>hash2</key>
			<data>
			HSPmaeLSu5PqNTtlwUWfRT4cyVX65JmeB/oed8f0pdU=
			</data>
		</dict>
		<key>ios-arm64/FirebaseAnalytics.framework/Headers/FIRAnalytics+Consent.h</key>
		<dict>
			<key>hash</key>
			<data>
			IN4riVkldIj3MlPpvlUuU1i+I8w=
			</data>
			<key>hash2</key>
			<data>
			ebGwpP2JZ0Rp6BdXhKiLUYOq6nJG7la5O0y/wEKfyak=
			</data>
		</dict>
		<key>ios-arm64/FirebaseAnalytics.framework/Headers/FIRAnalytics+OnDevice.h</key>
		<dict>
			<key>hash</key>
			<data>
			58LqQ/Y9jqPGz3JttzK0z7jPLLc=
			</data>
			<key>hash2</key>
			<data>
			67S/czxwflT8GxF7bVp32FLgu2W97zH06W6zv8/VILU=
			</data>
		</dict>
		<key>ios-arm64/FirebaseAnalytics.framework/Headers/FIRAnalytics.h</key>
		<dict>
			<key>hash</key>
			<data>
			B+YLaEHhiOu6tTPZaB5wWy4n95U=
			</data>
			<key>hash2</key>
			<data>
			wmpxZuP80odiQUX5Ts7GaPkhE7UeuzmDMrvTcOS0vLQ=
			</data>
		</dict>
		<key>ios-arm64/FirebaseAnalytics.framework/Headers/FIREventNames.h</key>
		<dict>
			<key>hash</key>
			<data>
			HrzLufMtB3E5zAfPXZTkYQdGyHI=
			</data>
			<key>hash2</key>
			<data>
			mNgPvQJ0O5ZQ/EINmxz+zoQHoxj/O70iH5crkbhGU3g=
			</data>
		</dict>
		<key>ios-arm64/FirebaseAnalytics.framework/Headers/FIRParameterNames.h</key>
		<dict>
			<key>hash</key>
			<data>
			SztBbCRZ5QE523pdMx4HZt0AYKE=
			</data>
			<key>hash2</key>
			<data>
			hwLb6dR4Q5L+j2vYcsXcc8sr2yoS1X9vyl7T/PjcHow=
			</data>
		</dict>
		<key>ios-arm64/FirebaseAnalytics.framework/Headers/FIRUserPropertyNames.h</key>
		<dict>
			<key>hash</key>
			<data>
			33ogzLW88kbc3XVXJp9Opq9Znmc=
			</data>
			<key>hash2</key>
			<data>
			z0lD0Agt0NzOZdG+xd6QpXvGS+06dK4A3RYK0Wu1OKw=
			</data>
		</dict>
		<key>ios-arm64/FirebaseAnalytics.framework/Headers/FirebaseAnalytics-Swift.h</key>
		<dict>
			<key>hash</key>
			<data>
			0nWXABx/HRqcUMkqMxRdJuHxqQU=
			</data>
			<key>hash2</key>
			<data>
			DmmG7v+JGyWpcKQl1BeGd8rqz4HV3b+PToUt+aM45bY=
			</data>
		</dict>
		<key>ios-arm64/FirebaseAnalytics.framework/Headers/FirebaseAnalytics-umbrella.h</key>
		<dict>
			<key>hash</key>
			<data>
			wE8Bjq1o5wGq1TFJb0gCJUmRelQ=
			</data>
			<key>hash2</key>
			<data>
			LOiywMHEh60MswGEzs9lM8P6m3oohYp+IWaiZtbwVVM=
			</data>
		</dict>
		<key>ios-arm64/FirebaseAnalytics.framework/Headers/FirebaseAnalytics.h</key>
		<dict>
			<key>hash</key>
			<data>
			6tM+QmAiCFyFHMaFXgWsH/uYosM=
			</data>
			<key>hash2</key>
			<data>
			VS1dxfDwCZeRcJYgkkEUhmdXx6ch9X/E8YjKBX367WY=
			</data>
		</dict>
		<key>ios-arm64/FirebaseAnalytics.framework/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			24GbDwHEExCsmAqEMaAnPX7hRng=
			</data>
			<key>hash2</key>
			<data>
			+0Wk0FpNzpPXbOvmO+ExDij1m7Qk6R1OkyKcUZr/6Ss=
			</data>
		</dict>
		<key>ios-arm64/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/Project/arm64-apple-ios.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			PAEFPn/Z87grIPt1TCrs17n93x0=
			</data>
			<key>hash2</key>
			<data>
			Sa0Xx20JBcDZ6Zt+d2Cc7eZzUesAa72xuYL3uRLIduc=
			</data>
		</dict>
		<key>ios-arm64/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			CAenCYd3LH1mXzWkxe5ejHMDkGU=
			</data>
			<key>hash2</key>
			<data>
			GEnEKb+ecq4eAklBmhX1bBXk9Sv4r3+yDxv3Lp8Vto4=
			</data>
		</dict>
		<key>ios-arm64/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			PTzHQuWqC1lwO+qReE+xp0WO1no=
			</data>
			<key>hash2</key>
			<data>
			br3Ok7WRBarJfQ21BS7G8x+55vkZ8wHoYLQ086ewiPU=
			</data>
		</dict>
		<key>ios-arm64/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			OaDLqia2HgSvNF2HhREihK6wwYI=
			</data>
			<key>hash2</key>
			<data>
			QxJ+lhdiHfZw4EPYjEQiZ/RpfCYC6szHehjJqzqtgU4=
			</data>
		</dict>
		<key>ios-arm64/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			PTzHQuWqC1lwO+qReE+xp0WO1no=
			</data>
			<key>hash2</key>
			<data>
			br3Ok7WRBarJfQ21BS7G8x+55vkZ8wHoYLQ086ewiPU=
			</data>
		</dict>
		<key>ios-arm64/FirebaseAnalytics.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			uLysK0T5K1GSoqJmcXx9AnZ9lqY=
			</data>
			<key>hash2</key>
			<data>
			vxNgOuI61t45Sed09vILAKePFm9riTp4aZ48hjDRPIQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/FirebaseAnalytics</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/FirebaseAnalytics</string>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Headers</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/Headers</string>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Modules</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/Modules</string>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Resources</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/Resources</string>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/FirebaseAnalytics</key>
		<dict>
			<key>hash</key>
			<data>
			42q76dI3y9rQrjqqUTNWwhZ7Jks=
			</data>
			<key>hash2</key>
			<data>
			E0RPZkffMxDh/6PM70UnvZC9j5Kk4G+3o9SQx9KpP+U=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Headers/FIRAnalytics+AppDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			JxSy4BVIpZB3s+tbI3EgcIVsvN0=
			</data>
			<key>hash2</key>
			<data>
			HSPmaeLSu5PqNTtlwUWfRT4cyVX65JmeB/oed8f0pdU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Headers/FIRAnalytics+Consent.h</key>
		<dict>
			<key>hash</key>
			<data>
			IN4riVkldIj3MlPpvlUuU1i+I8w=
			</data>
			<key>hash2</key>
			<data>
			ebGwpP2JZ0Rp6BdXhKiLUYOq6nJG7la5O0y/wEKfyak=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Headers/FIRAnalytics+OnDevice.h</key>
		<dict>
			<key>hash</key>
			<data>
			58LqQ/Y9jqPGz3JttzK0z7jPLLc=
			</data>
			<key>hash2</key>
			<data>
			67S/czxwflT8GxF7bVp32FLgu2W97zH06W6zv8/VILU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Headers/FIRAnalytics.h</key>
		<dict>
			<key>hash</key>
			<data>
			B+YLaEHhiOu6tTPZaB5wWy4n95U=
			</data>
			<key>hash2</key>
			<data>
			wmpxZuP80odiQUX5Ts7GaPkhE7UeuzmDMrvTcOS0vLQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Headers/FIREventNames.h</key>
		<dict>
			<key>hash</key>
			<data>
			HrzLufMtB3E5zAfPXZTkYQdGyHI=
			</data>
			<key>hash2</key>
			<data>
			mNgPvQJ0O5ZQ/EINmxz+zoQHoxj/O70iH5crkbhGU3g=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Headers/FIRParameterNames.h</key>
		<dict>
			<key>hash</key>
			<data>
			SztBbCRZ5QE523pdMx4HZt0AYKE=
			</data>
			<key>hash2</key>
			<data>
			hwLb6dR4Q5L+j2vYcsXcc8sr2yoS1X9vyl7T/PjcHow=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Headers/FIRUserPropertyNames.h</key>
		<dict>
			<key>hash</key>
			<data>
			33ogzLW88kbc3XVXJp9Opq9Znmc=
			</data>
			<key>hash2</key>
			<data>
			z0lD0Agt0NzOZdG+xd6QpXvGS+06dK4A3RYK0Wu1OKw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Headers/FirebaseAnalytics-Swift.h</key>
		<dict>
			<key>hash</key>
			<data>
			cu0mXcU7RrT9nKAMSamYxQcZ8MA=
			</data>
			<key>hash2</key>
			<data>
			4mnxjtoRMyo5TIjTieqzJLBQ4m0noIZqWhmI5a2a3QE=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Headers/FirebaseAnalytics-umbrella.h</key>
		<dict>
			<key>hash</key>
			<data>
			wE8Bjq1o5wGq1TFJb0gCJUmRelQ=
			</data>
			<key>hash2</key>
			<data>
			LOiywMHEh60MswGEzs9lM8P6m3oohYp+IWaiZtbwVVM=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Headers/FirebaseAnalytics.h</key>
		<dict>
			<key>hash</key>
			<data>
			6tM+QmAiCFyFHMaFXgWsH/uYosM=
			</data>
			<key>hash2</key>
			<data>
			VS1dxfDwCZeRcJYgkkEUhmdXx6ch9X/E8YjKBX367WY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/Project/arm64-apple-ios-macabi.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			HNZ2Jnf3ch0f2ZbmLUXV1LrWM/0=
			</data>
			<key>hash2</key>
			<data>
			2VPQzeBMhX0pMdIknacvcYDLca6YoapiXtYo9lCtjpw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/Project/x86_64-apple-ios-macabi.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			pJXoMAYTHcNc6A+wok94J0Z3PzQ=
			</data>
			<key>hash2</key>
			<data>
			0S8158R7Wqk1w7Gsp5eoBkz9iNBLEgqf5GoMkBUwaqE=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios-macabi.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			CAenCYd3LH1mXzWkxe5ejHMDkGU=
			</data>
			<key>hash2</key>
			<data>
			GEnEKb+ecq4eAklBmhX1bBXk9Sv4r3+yDxv3Lp8Vto4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios-macabi.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			4gR6vGh9NGCOc8NqbJitBi4PYMc=
			</data>
			<key>hash2</key>
			<data>
			sBsGCsKt/qVMlUX2JkA5hB1ko0klhw7cNGdqgUlwGW4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios-macabi.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			qhoelsD3haNmZEE9NM8xvtK9mEY=
			</data>
			<key>hash2</key>
			<data>
			ORG2lGLtqMk9O/g5fSRsgM0vkRuCeApJ2rKBJUOAyQY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios-macabi.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			4gR6vGh9NGCOc8NqbJitBi4PYMc=
			</data>
			<key>hash2</key>
			<data>
			sBsGCsKt/qVMlUX2JkA5hB1ko0klhw7cNGdqgUlwGW4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-ios-macabi.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			CAenCYd3LH1mXzWkxe5ejHMDkGU=
			</data>
			<key>hash2</key>
			<data>
			GEnEKb+ecq4eAklBmhX1bBXk9Sv4r3+yDxv3Lp8Vto4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-ios-macabi.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			R1LVVji18GwfW9nhhjO5aFGB1+k=
			</data>
			<key>hash2</key>
			<data>
			8sleA8PWyFsmq4UfIdcmC5GkHeUcPvp/kAytxpjR9N0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-ios-macabi.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			/8RdTbctZKCA1/OIwmZLl2oWnCk=
			</data>
			<key>hash2</key>
			<data>
			zgcFlEjAeUx/3gKqZtb0j32k5wK3ScunLgYWrpCZ8YY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-ios-macabi.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			R1LVVji18GwfW9nhhjO5aFGB1+k=
			</data>
			<key>hash2</key>
			<data>
			8sleA8PWyFsmq4UfIdcmC5GkHeUcPvp/kAytxpjR9N0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			uLysK0T5K1GSoqJmcXx9AnZ9lqY=
			</data>
			<key>hash2</key>
			<data>
			vxNgOuI61t45Sed09vILAKePFm9riTp4aZ48hjDRPIQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Resources/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			OzhUmy4DzK3Rxlq/bU6nkujbFmc=
			</data>
			<key>hash2</key>
			<data>
			AeZ++TIG/bG5AJUk2KoLdeUkd2hYmNfRUlApA/KqxeA=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/Current</key>
		<dict>
			<key>symlink</key>
			<string>A</string>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/FirebaseAnalytics</key>
		<dict>
			<key>hash</key>
			<data>
			QM6Y6wt9cmkeZQYdeaHFCtKfH4s=
			</data>
			<key>hash2</key>
			<data>
			w7DYEmRiLHOQ5aPRprGRUetoG2oAFRoRFR9GlVVfzSM=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRAnalytics+AppDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			JxSy4BVIpZB3s+tbI3EgcIVsvN0=
			</data>
			<key>hash2</key>
			<data>
			HSPmaeLSu5PqNTtlwUWfRT4cyVX65JmeB/oed8f0pdU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRAnalytics+Consent.h</key>
		<dict>
			<key>hash</key>
			<data>
			IN4riVkldIj3MlPpvlUuU1i+I8w=
			</data>
			<key>hash2</key>
			<data>
			ebGwpP2JZ0Rp6BdXhKiLUYOq6nJG7la5O0y/wEKfyak=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRAnalytics+OnDevice.h</key>
		<dict>
			<key>hash</key>
			<data>
			58LqQ/Y9jqPGz3JttzK0z7jPLLc=
			</data>
			<key>hash2</key>
			<data>
			67S/czxwflT8GxF7bVp32FLgu2W97zH06W6zv8/VILU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRAnalytics.h</key>
		<dict>
			<key>hash</key>
			<data>
			B+YLaEHhiOu6tTPZaB5wWy4n95U=
			</data>
			<key>hash2</key>
			<data>
			wmpxZuP80odiQUX5Ts7GaPkhE7UeuzmDMrvTcOS0vLQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIREventNames.h</key>
		<dict>
			<key>hash</key>
			<data>
			HrzLufMtB3E5zAfPXZTkYQdGyHI=
			</data>
			<key>hash2</key>
			<data>
			mNgPvQJ0O5ZQ/EINmxz+zoQHoxj/O70iH5crkbhGU3g=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRParameterNames.h</key>
		<dict>
			<key>hash</key>
			<data>
			SztBbCRZ5QE523pdMx4HZt0AYKE=
			</data>
			<key>hash2</key>
			<data>
			hwLb6dR4Q5L+j2vYcsXcc8sr2yoS1X9vyl7T/PjcHow=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRUserPropertyNames.h</key>
		<dict>
			<key>hash</key>
			<data>
			33ogzLW88kbc3XVXJp9Opq9Znmc=
			</data>
			<key>hash2</key>
			<data>
			z0lD0Agt0NzOZdG+xd6QpXvGS+06dK4A3RYK0Wu1OKw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FirebaseAnalytics-Swift.h</key>
		<dict>
			<key>hash</key>
			<data>
			cu0mXcU7RrT9nKAMSamYxQcZ8MA=
			</data>
			<key>hash2</key>
			<data>
			4mnxjtoRMyo5TIjTieqzJLBQ4m0noIZqWhmI5a2a3QE=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FirebaseAnalytics-umbrella.h</key>
		<dict>
			<key>hash</key>
			<data>
			wE8Bjq1o5wGq1TFJb0gCJUmRelQ=
			</data>
			<key>hash2</key>
			<data>
			LOiywMHEh60MswGEzs9lM8P6m3oohYp+IWaiZtbwVVM=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FirebaseAnalytics.h</key>
		<dict>
			<key>hash</key>
			<data>
			6tM+QmAiCFyFHMaFXgWsH/uYosM=
			</data>
			<key>hash2</key>
			<data>
			VS1dxfDwCZeRcJYgkkEUhmdXx6ch9X/E8YjKBX367WY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			a/ELmaanMcGm4o8ePSQhapLfN8g=
			</data>
			<key>hash2</key>
			<data>
			58i+E2SkLrV6L9whO09gQTUtS5aK54HqLMAYrtMy7Rs=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			DBvAE3VnjSOG5P478X3Putqnngk=
			</data>
			<key>hash2</key>
			<data>
			3GUNaeDj5IULtODG6QMQMCeOT0mQTt85nTGOF0U/wC0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			6XJlLJUNSEr2mzRdEdwredClK0o=
			</data>
			<key>hash2</key>
			<data>
			f/iG8zoAOHlFqLDp+1PS6wSJulrspJ02v5XVqhIHZPc=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			CAenCYd3LH1mXzWkxe5ejHMDkGU=
			</data>
			<key>hash2</key>
			<data>
			GEnEKb+ecq4eAklBmhX1bBXk9Sv4r3+yDxv3Lp8Vto4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios-simulator.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			QinHhX49jrG/LCXfKglixhg2PPE=
			</data>
			<key>hash2</key>
			<data>
			TlTGbdekTMP/MKLUWM3I9V2KTXERNtHCAgztCGg7v4g=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			2uhhvWC34TCjqIKVVlqG/EycMGY=
			</data>
			<key>hash2</key>
			<data>
			9vcaXzpRIBFJPDzlyoFUMY+TisZbde7d06MG8QD5Ucc=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios-simulator.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			QinHhX49jrG/LCXfKglixhg2PPE=
			</data>
			<key>hash2</key>
			<data>
			TlTGbdekTMP/MKLUWM3I9V2KTXERNtHCAgztCGg7v4g=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			CAenCYd3LH1mXzWkxe5ejHMDkGU=
			</data>
			<key>hash2</key>
			<data>
			GEnEKb+ecq4eAklBmhX1bBXk9Sv4r3+yDxv3Lp8Vto4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			0qZBNnrrWfJHxGSgyJ0H3df9gbQ=
			</data>
			<key>hash2</key>
			<data>
			NIoF41mM+5NzuoQ+Lx9c6KvvJoqHCMreUqxMD+kS84w=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			lW9AObcye0EJ2DSm5O07/pplg2Y=
			</data>
			<key>hash2</key>
			<data>
			7oUz7+K/zFoxTeMYF6mlBUWZWez922QqrjcxTxecVQc=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			0qZBNnrrWfJHxGSgyJ0H3df9gbQ=
			</data>
			<key>hash2</key>
			<data>
			NIoF41mM+5NzuoQ+Lx9c6KvvJoqHCMreUqxMD+kS84w=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			uLysK0T5K1GSoqJmcXx9AnZ9lqY=
			</data>
			<key>hash2</key>
			<data>
			vxNgOuI61t45Sed09vILAKePFm9riTp4aZ48hjDRPIQ=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/FirebaseAnalytics</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/FirebaseAnalytics</string>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Headers</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/Headers</string>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Modules</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/Modules</string>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Resources</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/Resources</string>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/FirebaseAnalytics</key>
		<dict>
			<key>hash</key>
			<data>
			vOVrRvYINMVRHZwAzEWPe6FgfsA=
			</data>
			<key>hash2</key>
			<data>
			sJi2CZLi+k7RexEpsq3RLjcLzR9+lmZ1dqmykV66VWQ=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Headers/FIRAnalytics+AppDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			JxSy4BVIpZB3s+tbI3EgcIVsvN0=
			</data>
			<key>hash2</key>
			<data>
			HSPmaeLSu5PqNTtlwUWfRT4cyVX65JmeB/oed8f0pdU=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Headers/FIRAnalytics+Consent.h</key>
		<dict>
			<key>hash</key>
			<data>
			IN4riVkldIj3MlPpvlUuU1i+I8w=
			</data>
			<key>hash2</key>
			<data>
			ebGwpP2JZ0Rp6BdXhKiLUYOq6nJG7la5O0y/wEKfyak=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Headers/FIRAnalytics+OnDevice.h</key>
		<dict>
			<key>hash</key>
			<data>
			58LqQ/Y9jqPGz3JttzK0z7jPLLc=
			</data>
			<key>hash2</key>
			<data>
			67S/czxwflT8GxF7bVp32FLgu2W97zH06W6zv8/VILU=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Headers/FIRAnalytics.h</key>
		<dict>
			<key>hash</key>
			<data>
			B+YLaEHhiOu6tTPZaB5wWy4n95U=
			</data>
			<key>hash2</key>
			<data>
			wmpxZuP80odiQUX5Ts7GaPkhE7UeuzmDMrvTcOS0vLQ=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Headers/FIREventNames.h</key>
		<dict>
			<key>hash</key>
			<data>
			HrzLufMtB3E5zAfPXZTkYQdGyHI=
			</data>
			<key>hash2</key>
			<data>
			mNgPvQJ0O5ZQ/EINmxz+zoQHoxj/O70iH5crkbhGU3g=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Headers/FIRParameterNames.h</key>
		<dict>
			<key>hash</key>
			<data>
			SztBbCRZ5QE523pdMx4HZt0AYKE=
			</data>
			<key>hash2</key>
			<data>
			hwLb6dR4Q5L+j2vYcsXcc8sr2yoS1X9vyl7T/PjcHow=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Headers/FIRUserPropertyNames.h</key>
		<dict>
			<key>hash</key>
			<data>
			33ogzLW88kbc3XVXJp9Opq9Znmc=
			</data>
			<key>hash2</key>
			<data>
			z0lD0Agt0NzOZdG+xd6QpXvGS+06dK4A3RYK0Wu1OKw=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Headers/FirebaseAnalytics-Swift.h</key>
		<dict>
			<key>hash</key>
			<data>
			cu0mXcU7RrT9nKAMSamYxQcZ8MA=
			</data>
			<key>hash2</key>
			<data>
			4mnxjtoRMyo5TIjTieqzJLBQ4m0noIZqWhmI5a2a3QE=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Headers/FirebaseAnalytics-umbrella.h</key>
		<dict>
			<key>hash</key>
			<data>
			sXk7jWhcfsvb8eJb7/NDl2Dy2RQ=
			</data>
			<key>hash2</key>
			<data>
			E6EdPyZp5cQbOObE0CPO9/R+QvTIYlDIOHvuzam/9H8=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Headers/FirebaseAnalytics.h</key>
		<dict>
			<key>hash</key>
			<data>
			6tM+QmAiCFyFHMaFXgWsH/uYosM=
			</data>
			<key>hash2</key>
			<data>
			VS1dxfDwCZeRcJYgkkEUhmdXx6ch9X/E8YjKBX367WY=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			oHwEH8nDNQLi2WiJYNHluEM3dr8=
			</data>
			<key>hash2</key>
			<data>
			Keh8JvoRVOXlOzPd3B/1BsW1UwRAbS0V21+rnW+8hVs=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/Project/x86_64-apple-macos.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			5D2gHeLqXEwVMH2q9LsSCJvZ3Hw=
			</data>
			<key>hash2</key>
			<data>
			2EhWnogwbbF2WdZk0jjmgzul4xH63cPgK9JXySMkt8Q=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-macos.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			CAenCYd3LH1mXzWkxe5ejHMDkGU=
			</data>
			<key>hash2</key>
			<data>
			GEnEKb+ecq4eAklBmhX1bBXk9Sv4r3+yDxv3Lp8Vto4=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-macos.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			8GJRLSkXpWDzmNzbFI7E5MPaxHs=
			</data>
			<key>hash2</key>
			<data>
			e2hx6o7dBwggM/REp3KsbbHHOSCXakob3UTMbtQSxmQ=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-macos.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			/do/AsQ2BlA5B8FGwqiGoGnBanw=
			</data>
			<key>hash2</key>
			<data>
			sC/kNiIq68DXBpk2mHvINyOXPLhWhZYdt1nrYgyPno4=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-macos.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			8GJRLSkXpWDzmNzbFI7E5MPaxHs=
			</data>
			<key>hash2</key>
			<data>
			e2hx6o7dBwggM/REp3KsbbHHOSCXakob3UTMbtQSxmQ=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-macos.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			CAenCYd3LH1mXzWkxe5ejHMDkGU=
			</data>
			<key>hash2</key>
			<data>
			GEnEKb+ecq4eAklBmhX1bBXk9Sv4r3+yDxv3Lp8Vto4=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-macos.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			IEN4ZkUAv93Vhe3Ywy2Ti9s/jSw=
			</data>
			<key>hash2</key>
			<data>
			nNMkbhWYh8cQ2Al4vTVQUuiZ28HKOA1BRk6Ao7x9eqA=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-macos.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			bobvJ33JqODvJfRuJhtThgX6/9U=
			</data>
			<key>hash2</key>
			<data>
			s/GI6pRR4LifbiY3tDjp006X3w5vfsCnwrbTEnUzqbk=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-macos.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			IEN4ZkUAv93Vhe3Ywy2Ti9s/jSw=
			</data>
			<key>hash2</key>
			<data>
			nNMkbhWYh8cQ2Al4vTVQUuiZ28HKOA1BRk6Ao7x9eqA=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			jWZ+azdgXQdH9z56z25NptmWlq4=
			</data>
			<key>hash2</key>
			<data>
			e4a41Axjw7BAywMms/GuOcqrdwPIVYuMRsPznqQS0X8=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Resources/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			Ohh69ZLP3Pwmu3LTZbXKbBKfbGU=
			</data>
			<key>hash2</key>
			<data>
			1NXhAH/bxGdY9nDcn9Hcf7YNiIRiAaq0GKCE6dLU0K4=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/Current</key>
		<dict>
			<key>symlink</key>
			<string>A</string>
		</dict>
		<key>tvos-arm64/FirebaseAnalytics.framework/FirebaseAnalytics</key>
		<dict>
			<key>hash</key>
			<data>
			W8q251W3WIRDyG0bRchN+yib+J0=
			</data>
			<key>hash2</key>
			<data>
			3jWhbmmpaSTf8eHh+ItV1KkSi2/rvbRi9I9HivNb2ZQ=
			</data>
		</dict>
		<key>tvos-arm64/FirebaseAnalytics.framework/Headers/FIRAnalytics+AppDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			JxSy4BVIpZB3s+tbI3EgcIVsvN0=
			</data>
			<key>hash2</key>
			<data>
			HSPmaeLSu5PqNTtlwUWfRT4cyVX65JmeB/oed8f0pdU=
			</data>
		</dict>
		<key>tvos-arm64/FirebaseAnalytics.framework/Headers/FIRAnalytics+Consent.h</key>
		<dict>
			<key>hash</key>
			<data>
			IN4riVkldIj3MlPpvlUuU1i+I8w=
			</data>
			<key>hash2</key>
			<data>
			ebGwpP2JZ0Rp6BdXhKiLUYOq6nJG7la5O0y/wEKfyak=
			</data>
		</dict>
		<key>tvos-arm64/FirebaseAnalytics.framework/Headers/FIRAnalytics+OnDevice.h</key>
		<dict>
			<key>hash</key>
			<data>
			58LqQ/Y9jqPGz3JttzK0z7jPLLc=
			</data>
			<key>hash2</key>
			<data>
			67S/czxwflT8GxF7bVp32FLgu2W97zH06W6zv8/VILU=
			</data>
		</dict>
		<key>tvos-arm64/FirebaseAnalytics.framework/Headers/FIRAnalytics.h</key>
		<dict>
			<key>hash</key>
			<data>
			B+YLaEHhiOu6tTPZaB5wWy4n95U=
			</data>
			<key>hash2</key>
			<data>
			wmpxZuP80odiQUX5Ts7GaPkhE7UeuzmDMrvTcOS0vLQ=
			</data>
		</dict>
		<key>tvos-arm64/FirebaseAnalytics.framework/Headers/FIREventNames.h</key>
		<dict>
			<key>hash</key>
			<data>
			HrzLufMtB3E5zAfPXZTkYQdGyHI=
			</data>
			<key>hash2</key>
			<data>
			mNgPvQJ0O5ZQ/EINmxz+zoQHoxj/O70iH5crkbhGU3g=
			</data>
		</dict>
		<key>tvos-arm64/FirebaseAnalytics.framework/Headers/FIRParameterNames.h</key>
		<dict>
			<key>hash</key>
			<data>
			SztBbCRZ5QE523pdMx4HZt0AYKE=
			</data>
			<key>hash2</key>
			<data>
			hwLb6dR4Q5L+j2vYcsXcc8sr2yoS1X9vyl7T/PjcHow=
			</data>
		</dict>
		<key>tvos-arm64/FirebaseAnalytics.framework/Headers/FIRUserPropertyNames.h</key>
		<dict>
			<key>hash</key>
			<data>
			33ogzLW88kbc3XVXJp9Opq9Znmc=
			</data>
			<key>hash2</key>
			<data>
			z0lD0Agt0NzOZdG+xd6QpXvGS+06dK4A3RYK0Wu1OKw=
			</data>
		</dict>
		<key>tvos-arm64/FirebaseAnalytics.framework/Headers/FirebaseAnalytics-Swift.h</key>
		<dict>
			<key>hash</key>
			<data>
			0nWXABx/HRqcUMkqMxRdJuHxqQU=
			</data>
			<key>hash2</key>
			<data>
			DmmG7v+JGyWpcKQl1BeGd8rqz4HV3b+PToUt+aM45bY=
			</data>
		</dict>
		<key>tvos-arm64/FirebaseAnalytics.framework/Headers/FirebaseAnalytics-umbrella.h</key>
		<dict>
			<key>hash</key>
			<data>
			wE8Bjq1o5wGq1TFJb0gCJUmRelQ=
			</data>
			<key>hash2</key>
			<data>
			LOiywMHEh60MswGEzs9lM8P6m3oohYp+IWaiZtbwVVM=
			</data>
		</dict>
		<key>tvos-arm64/FirebaseAnalytics.framework/Headers/FirebaseAnalytics.h</key>
		<dict>
			<key>hash</key>
			<data>
			6tM+QmAiCFyFHMaFXgWsH/uYosM=
			</data>
			<key>hash2</key>
			<data>
			VS1dxfDwCZeRcJYgkkEUhmdXx6ch9X/E8YjKBX367WY=
			</data>
		</dict>
		<key>tvos-arm64/FirebaseAnalytics.framework/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			6aXVQGe8JpqinvGQ6cWiSR2Srr0=
			</data>
			<key>hash2</key>
			<data>
			MEo/VlhVyQzbU25ehCWenLWsC54G8TJ6Lh32bT18x/E=
			</data>
		</dict>
		<key>tvos-arm64/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/Project/arm64-apple-tvos.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			zxaMs9ukSdkkXZqwV3vOHeWr7+g=
			</data>
			<key>hash2</key>
			<data>
			gOHS+I3HgFQUtLQXwYegHJST0BxhyMiCiSZWOXr7Yrg=
			</data>
		</dict>
		<key>tvos-arm64/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-tvos.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			CAenCYd3LH1mXzWkxe5ejHMDkGU=
			</data>
			<key>hash2</key>
			<data>
			GEnEKb+ecq4eAklBmhX1bBXk9Sv4r3+yDxv3Lp8Vto4=
			</data>
		</dict>
		<key>tvos-arm64/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-tvos.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			w4z4Z/v+RhKvKZ9rpQwngKxdIv8=
			</data>
			<key>hash2</key>
			<data>
			Sfi67ylcgLV25NSmMgDoWPHJOM5fdckJgZxpO7ao65Q=
			</data>
		</dict>
		<key>tvos-arm64/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-tvos.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			hXtRlFapwJDHg4NuSRJKOZnPtf4=
			</data>
			<key>hash2</key>
			<data>
			MX02TrGF3PcXcv2k6L48iDd2uiemvkpx5z0huyC97yI=
			</data>
		</dict>
		<key>tvos-arm64/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-tvos.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			w4z4Z/v+RhKvKZ9rpQwngKxdIv8=
			</data>
			<key>hash2</key>
			<data>
			Sfi67ylcgLV25NSmMgDoWPHJOM5fdckJgZxpO7ao65Q=
			</data>
		</dict>
		<key>tvos-arm64/FirebaseAnalytics.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			uLysK0T5K1GSoqJmcXx9AnZ9lqY=
			</data>
			<key>hash2</key>
			<data>
			vxNgOuI61t45Sed09vILAKePFm9riTp4aZ48hjDRPIQ=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/FirebaseAnalytics</key>
		<dict>
			<key>hash</key>
			<data>
			9mJR8rSmTn7mt3W8hEw0jnxQfN0=
			</data>
			<key>hash2</key>
			<data>
			y3nl2jb5AucJqDdYs2geys5kakZ1p+ERjfHY8/HKe08=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRAnalytics+AppDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			JxSy4BVIpZB3s+tbI3EgcIVsvN0=
			</data>
			<key>hash2</key>
			<data>
			HSPmaeLSu5PqNTtlwUWfRT4cyVX65JmeB/oed8f0pdU=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRAnalytics+Consent.h</key>
		<dict>
			<key>hash</key>
			<data>
			IN4riVkldIj3MlPpvlUuU1i+I8w=
			</data>
			<key>hash2</key>
			<data>
			ebGwpP2JZ0Rp6BdXhKiLUYOq6nJG7la5O0y/wEKfyak=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRAnalytics+OnDevice.h</key>
		<dict>
			<key>hash</key>
			<data>
			58LqQ/Y9jqPGz3JttzK0z7jPLLc=
			</data>
			<key>hash2</key>
			<data>
			67S/czxwflT8GxF7bVp32FLgu2W97zH06W6zv8/VILU=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRAnalytics.h</key>
		<dict>
			<key>hash</key>
			<data>
			B+YLaEHhiOu6tTPZaB5wWy4n95U=
			</data>
			<key>hash2</key>
			<data>
			wmpxZuP80odiQUX5Ts7GaPkhE7UeuzmDMrvTcOS0vLQ=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIREventNames.h</key>
		<dict>
			<key>hash</key>
			<data>
			HrzLufMtB3E5zAfPXZTkYQdGyHI=
			</data>
			<key>hash2</key>
			<data>
			mNgPvQJ0O5ZQ/EINmxz+zoQHoxj/O70iH5crkbhGU3g=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRParameterNames.h</key>
		<dict>
			<key>hash</key>
			<data>
			SztBbCRZ5QE523pdMx4HZt0AYKE=
			</data>
			<key>hash2</key>
			<data>
			hwLb6dR4Q5L+j2vYcsXcc8sr2yoS1X9vyl7T/PjcHow=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRUserPropertyNames.h</key>
		<dict>
			<key>hash</key>
			<data>
			33ogzLW88kbc3XVXJp9Opq9Znmc=
			</data>
			<key>hash2</key>
			<data>
			z0lD0Agt0NzOZdG+xd6QpXvGS+06dK4A3RYK0Wu1OKw=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FirebaseAnalytics-Swift.h</key>
		<dict>
			<key>hash</key>
			<data>
			cu0mXcU7RrT9nKAMSamYxQcZ8MA=
			</data>
			<key>hash2</key>
			<data>
			4mnxjtoRMyo5TIjTieqzJLBQ4m0noIZqWhmI5a2a3QE=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FirebaseAnalytics-umbrella.h</key>
		<dict>
			<key>hash</key>
			<data>
			wE8Bjq1o5wGq1TFJb0gCJUmRelQ=
			</data>
			<key>hash2</key>
			<data>
			LOiywMHEh60MswGEzs9lM8P6m3oohYp+IWaiZtbwVVM=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FirebaseAnalytics.h</key>
		<dict>
			<key>hash</key>
			<data>
			6tM+QmAiCFyFHMaFXgWsH/uYosM=
			</data>
			<key>hash2</key>
			<data>
			VS1dxfDwCZeRcJYgkkEUhmdXx6ch9X/E8YjKBX367WY=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			kXJVN191XwsiAoBr3QwAXliqK64=
			</data>
			<key>hash2</key>
			<data>
			IAsXw/OaWU76/Q0OIIIapV6H+GVvhAU3jIt8IihZu0U=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/Project/arm64-apple-tvos-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			AaIax21fSETeDoYqQ8cVecFc1DA=
			</data>
			<key>hash2</key>
			<data>
			GGcwJkjXOLxAWVw4WV1e07f7eJQuc/5ekkXIaYmdDWw=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/Project/x86_64-apple-tvos-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			r/Iaqox1b0S0D1nrKMmpUz/RYs0=
			</data>
			<key>hash2</key>
			<data>
			pCtKReeUwXR7F2uPXOK5QZqrXQnZM+F4dppUZiiB5Lg=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-tvos-simulator.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			CAenCYd3LH1mXzWkxe5ejHMDkGU=
			</data>
			<key>hash2</key>
			<data>
			GEnEKb+ecq4eAklBmhX1bBXk9Sv4r3+yDxv3Lp8Vto4=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-tvos-simulator.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			Hf8XfWJzIeEvfdJv/6AMdURHy9k=
			</data>
			<key>hash2</key>
			<data>
			J12IabDa2wBWnzPjUNWL2pBviYvTzf1rbHBRVuWs1yM=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-tvos-simulator.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			6OlHbfwffHQPeO54yIdFziP5xxg=
			</data>
			<key>hash2</key>
			<data>
			qLojQ1uaKdXtuP2lnuY8lqCxNQJtWP1gKD2DOEiofqI=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-tvos-simulator.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			Hf8XfWJzIeEvfdJv/6AMdURHy9k=
			</data>
			<key>hash2</key>
			<data>
			J12IabDa2wBWnzPjUNWL2pBviYvTzf1rbHBRVuWs1yM=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-tvos-simulator.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			CAenCYd3LH1mXzWkxe5ejHMDkGU=
			</data>
			<key>hash2</key>
			<data>
			GEnEKb+ecq4eAklBmhX1bBXk9Sv4r3+yDxv3Lp8Vto4=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-tvos-simulator.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			/J9MSWx/8Fj0oVu98k1n8XZmnUE=
			</data>
			<key>hash2</key>
			<data>
			Ql4EhD2ABm1fyH6xOVmiypvWz+aa/1H7xcVrRLBvdN4=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-tvos-simulator.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			foK4LqUJlIKa+nt0y/51HYQeK8I=
			</data>
			<key>hash2</key>
			<data>
			T5Y6leTow01MGnnXreNqyewOJuClXDew2dg53dtVmDU=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-tvos-simulator.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			/J9MSWx/8Fj0oVu98k1n8XZmnUE=
			</data>
			<key>hash2</key>
			<data>
			Ql4EhD2ABm1fyH6xOVmiypvWz+aa/1H7xcVrRLBvdN4=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			uLysK0T5K1GSoqJmcXx9AnZ9lqY=
			</data>
			<key>hash2</key>
			<data>
			vxNgOuI61t45Sed09vILAKePFm9riTp4aZ48hjDRPIQ=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
