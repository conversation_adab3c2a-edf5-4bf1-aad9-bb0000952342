PODS:
  - FBAudienceNetwork (6.15.0)
  - Firebase/Core (10.25.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 10.25.0)
  - Firebase/CoreOnly (10.25.0):
    - FirebaseCore (= 10.25.0)
  - Firebase/Messaging (10.25.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 10.25.0)
  - FirebaseAnalytics (10.25.0):
    - FirebaseAnalytics/AdIdSupport (= 10.25.0)
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - FirebaseAnalytics/AdIdSupport (10.25.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleAppMeasurement (= 10.25.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - FirebaseCore (10.25.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.12)
    - GoogleUtilities/Logger (~> 7.12)
  - FirebaseCoreInternal (10.25.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseInstallations (10.25.0):
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - PromisesObjC (~> 2.1)
  - FirebaseMessaging (10.25.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.3)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Reachability (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - Google-Mobile-Ads-SDK (11.4.0):
    - GoogleUserMessagingPlatform (>= 1.1)
  - GoogleAppMeasurement (10.25.0):
    - GoogleAppMeasurement/AdIdSupport (= 10.25.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - GoogleAppMeasurement/AdIdSupport (10.25.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 10.25.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (10.25.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - GoogleDataTransport (9.4.1):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUserMessagingPlatform (2.4.0)
  - GoogleUtilities/AppDelegateSwizzler (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (7.13.3):
    - GoogleUtilities/Privacy
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (7.13.3):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.13.3)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (7.13.3)
  - GoogleUtilities/Reachability (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - nanopb (2.30910.0):
    - nanopb/decode (= 2.30910.0)
    - nanopb/encode (= 2.30910.0)
  - nanopb/decode (2.30910.0)
  - nanopb/encode (2.30910.0)
  - OneSignalXCFramework (3.12.9):
    - OneSignalXCFramework/OneSignalCore (= 3.12.9)
    - OneSignalXCFramework/OneSignalExtension (= 3.12.9)
    - OneSignalXCFramework/OneSignalOutcomes (= 3.12.9)
  - OneSignalXCFramework/OneSignalCore (3.12.9)
  - OneSignalXCFramework/OneSignalExtension (3.12.9):
    - OneSignalXCFramework/OneSignalCore
    - OneSignalXCFramework/OneSignalOutcomes
  - OneSignalXCFramework/OneSignalOutcomes (3.12.9):
    - OneSignalXCFramework/OneSignalCore
  - PromisesObjC (2.4.0)
  - PushwooshXCFramework (6.5.14):
    - PushwooshXCFramework/Core (= 6.5.14)
  - PushwooshXCFramework/Core (6.5.14)
  - SwiftQRScanner (0.1.0)
  - SwiftyGif (5.4.5)
  - SwiftyStoreKit (0.16.1)

DEPENDENCIES:
  - FBAudienceNetwork
  - Firebase/Core
  - Firebase/Messaging
  - Google-Mobile-Ads-SDK
  - GoogleUserMessagingPlatform
  - OneSignalXCFramework (= 3.12.9)
  - PushwooshXCFramework
  - SwiftQRScanner
  - SwiftyGif
  - SwiftyStoreKit

SPEC REPOS:
  trunk:
    - FBAudienceNetwork
    - Firebase
    - FirebaseAnalytics
    - FirebaseCore
    - FirebaseCoreInternal
    - FirebaseInstallations
    - FirebaseMessaging
    - Google-Mobile-Ads-SDK
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleUserMessagingPlatform
    - GoogleUtilities
    - nanopb
    - OneSignalXCFramework
    - PromisesObjC
    - PushwooshXCFramework
    - SwiftQRScanner
    - SwiftyGif
    - SwiftyStoreKit

SPEC CHECKSUMS:
  FBAudienceNetwork: 80a035aa69cc3a71c99e44cb5c2874e4cef9909f
  Firebase: 0312a2352584f782ea56f66d91606891d4607f06
  FirebaseAnalytics: ec00fe8b93b41dc6fe4a28784b8e51da0647a248
  FirebaseCore: 7ec4d0484817f12c3373955bc87762d96842d483
  FirebaseCoreInternal: 910a81992c33715fec9263ca7381d59ab3a750b7
  FirebaseInstallations: 91950fe859846fff0fbd296180909dd273103b09
  FirebaseMessaging: 88950ba9485052891ebe26f6c43a52bb62248952
  Google-Mobile-Ads-SDK: d097dca17b9344135dcf592e79df0c7fbd89947b
  GoogleAppMeasurement: 9abf64b682732fed36da827aa2a68f0221fd2356
  GoogleDataTransport: 6c09b596d841063d76d4288cc2d2f42cc36e1e2a
  GoogleUserMessagingPlatform: f131fa7978d2ba88d7426702b057c2cc318e6595
  GoogleUtilities: ea963c370a38a8069cc5f7ba4ca849a60b6d7d15
  nanopb: 438bc412db1928dac798aa6fd75726007be04262
  OneSignalXCFramework: 3c3ef25ecca5eae1bf80a3272c49fd1dbab96b35
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  PushwooshXCFramework: 280313a2a559bbd9830dc9519f1601255d797aa4
  SwiftQRScanner: fba218bce4e5ac5de8ab6b72c6c7fefa09d29c5b
  SwiftyGif: 706c60cf65fa2bc5ee0313beece843c8eb8194d4
  SwiftyStoreKit: 6b9c08810269f030586dac1fae8e75871a82e84a

PODFILE CHECKSUM: 881f1f7edfdbb004374bd20b641fde46c4aa8c6c

COCOAPODS: 1.16.2
